<template>
  <div id="app">
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-green sticky-top">
      <div class="container">
        <span class="navbar-brand">植物基底饮食屋</span>
        <div class="ml-auto d-flex align-items-center">
          <!-- 购物车按钮 -->
          <button class="btn btn-outline-light position-relative mr-2" @click="openCart">
            <i class="bi bi-cart3"></i>
            <!-- 购物车数量徽章 -->
            <span v-if="cartItemCount > 0" class="cart-badge badge badge-danger position-absolute">
              {{ cartItemCount }}
            </span>
          </button>
          <!-- 菜单按钮 -->
          <button class="navbar-toggler border-0" type="button" data-toggle="collapse" data-target="#navbarContent">
            <span class="navbar-toggler-icon"></span>
          </button>
        </div>
      </div>
      <!-- 折叠菜单 -->
      <div class="collapse navbar-collapse bg-green" id="navbarContent">
        <div class="container">
          <ul class="navbar-nav">
            <li class="nav-item active">
              <a class="nav-link" href="#">首页</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#">菜单</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#">关于我们</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#">联系方式</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- 食品展示区 -->
    <section id="food-section" class="py-3">
      <div class="container">
        <div class="row" id="food-container">
          <!-- 使用Vue组件显示食品卡片 -->
          <div v-for="(food, index) in foods" :key="index" class="col-12 col-sm-12 col-md-6 col-lg-3 mb-4">
            <div class="card food-card border-0">
              <div class="food-img-container position-relative">
                <img :src="food.foodImage" class="card-img-top food-img" :alt="food.foodName">
                <!-- 添加库存标签 -->
                <span v-if="food.stock <= 10" class="stock-badge badge badge-warning position-absolute">
                  仅剩 {{ food.stock }} 份
                </span>
              </div>
              <div class="card-body px-2">
                <h5 class="card-title">{{ food.foodName }}</h5>
                <p class="card-text mb-1">价格: <span class="text-danger font-weight-bold">¥{{ food.foodPrice }}</span></p>
                <div class="d-flex justify-content-between mt-2">
                  <button class="btn btn-sm btn-outline-success add-cart-btn" @click="addToCart(index)">
                    <i class="bi bi-cart-plus"></i> 加入购物车
                  </button>
                  <button class="btn btn-sm btn-primary detail-btn" @click="showFoodDetail(index)">
                    详情
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 购物车模态框 -->
    <div class="modal fade" id="cartModal" tabindex="-1" aria-labelledby="cartModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header bg-green text-white">
            <h5 class="modal-title" id="cartModalLabel">购物车</h5>
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div id="cart-items" v-if="cart.length > 0">
              <div class="table-responsive">
                <table class="table">
                  <thead>
                    <tr>
                      <th>商品</th>
                      <th>单价</th>
                      <th>数量</th>
                      <th>小计</th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in cart" :key="index">
                      <td>
                        <div class="d-flex align-items-center">
                          <img :src="item.food.foodImage" :alt="item.food.foodName" style="width: 50px; height: 50px; object-fit: cover;" class="mr-2">
                          <span>{{ item.food.foodName }}</span>
                        </div>
                      </td>
                      <td>¥{{ item.food.foodPrice }}</td>
                      <td>
                        <div class="input-group input-group-sm" style="width: 120px;">
                          <div class="input-group-prepend">
                            <button class="btn btn-outline-secondary" type="button" @click="decreaseQuantity(index)">-</button>
                          </div>
                          <input type="text" class="form-control text-center" :value="item.quantity" readonly>
                          <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button" @click="increaseQuantity(index)">+</button>
                          </div>
                        </div>
                      </td>
                      <td>¥{{ item.food.foodPrice * item.quantity }}</td>
                      <td>
                        <button class="btn btn-sm btn-outline-danger" @click="removeFromCart(index)">
                          <i class="bi bi-trash"></i>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div id="empty-cart-message" class="text-center py-4" v-if="cart.length === 0">
              <i class="bi bi-cart3 display-4 text-muted"></i>
              <p class="mt-3">您的购物车是空的</p>
            </div>
          </div>
          <div class="modal-footer justify-content-between">
            <div>
              <strong>总计: <span id="cart-total">¥{{ cartTotal }}</span></strong>
            </div>
            <div>
              <button type="button" class="btn btn-secondary" data-dismiss="modal">继续购物</button>
              <button type="button" class="btn btn-green" id="checkout-btn" @click="checkout">结算</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 食品详情模态框 -->
    <div class="modal fade" id="foodDetailModal" tabindex="-1" aria-labelledby="foodDetailModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-light">
            <h5 class="modal-title" id="foodDetailModalLabel" v-if="selectedFood">{{ selectedFood.foodName }}</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body p-0" v-if="selectedFood">
            <!-- 食品图片 -->
            <div class="food-detail-img-container">
              <img :src="selectedFood.foodImage" class="img-fluid w-100" alt="食品图片">
              <!-- 价格标签 -->
              <div class="price-tag position-absolute bg-success text-white py-1 px-3 rounded-right">
                ¥{{ selectedFood.foodPrice }}
              </div>
            </div>

            <!-- 食品信息 -->
            <div class="p-3">
              <!-- 库存信息 -->
              <div class="d-flex justify-content-between align-items-center mb-3">
                <span class="badge" :class="selectedFood.stock > 10 ? 'badge-success' : 'badge-warning'">
                  库存: {{ selectedFood.stock }}
                </span>
                <div class="d-flex align-items-center">
                  <span class="mr-2">数量:</span>
                  <div class="input-group input-group-sm" style="width: 120px;">
                    <div class="input-group-prepend">
                      <button class="btn btn-outline-secondary" type="button" @click="decreaseDetailQuantity">-</button>
                    </div>
                    <input type="text" class="form-control text-center" v-model="detailQuantity" readonly>
                    <div class="input-group-append">
                      <button class="btn btn-outline-secondary" type="button" @click="increaseDetailQuantity">+</button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 描述 -->
              <h6 class="font-weight-bold mb-2">商品描述</h6>
              <p class="food-description">{{ selectedFood.foodDescription }}</p>

              <!-- 购买信息 -->
              <div v-if="purchaseMessage" class="alert" :class="purchaseMessage.includes('成功') ? 'alert-success' : 'alert-danger'" v-html="purchaseMessage"></div>
            </div>
          </div>
          <div class="modal-footer">
            <button class="btn btn-outline-secondary" data-dismiss="modal">关闭</button>
            <button class="btn btn-success" @click="addToCartFromDetail">
              <i class="bi bi-cart-plus"></i> 加入购物车
            </button>
            <button class="btn btn-primary" @click="processPurchaseDirectly">
              立即购买
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 结算成功模态框 -->
    <div class="modal fade" id="checkoutSuccessModal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header bg-success text-white">
            <h5 class="modal-title">订单成功</h5>
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body text-center py-4">
            <i class="bi bi-check-circle display-1 text-success"></i>
            <h4 class="mt-3">您的订单已成功提交！</h4>
            <p class="text-muted">感谢您的购买，我们将尽快处理您的订单。</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-success" data-dismiss="modal">确定</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-2">
      <div class="container">
        <div class="text-center">
          <p class="mb-0">&copy; 2025 植物基底饮食屋. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      // 购物车数据
      cart: [],
      // 商品数据
      foods: [
        {
          "foodName": "沙拉碗",
          "foodPrice": 25,
          "foodImage": "images/food1.png",
          "foodDescription": "新鲜制作的沙拉碗，选用当季蔬菜与优质生菜搭配，富含膳食纤维和多种维生素。低脂酱料让口感更清爽，是健康饮食的首选。",
          "stock": 1
        },
        {
          "foodName": "杂粮碗",
          "foodPrice": 22,
          "foodImage": "images/food2.png",
          "foodDescription": "杂粮碗由五种精选粗粮精心熬制而成，富含丰富的碳水化合物和微量元素，有助于增强饱腹感，适合健身人士和追求健康的你。",
          "stock": 80
        },
        {
          "foodName": "酸奶碗",
          "foodPrice": 18,
          "foodImage": "images/food3.png",
          "foodDescription": "酸奶碗采用天然发酵酸奶，搭配少量蜂蜜和坚果，口感醇厚，富含益生菌，帮助调节肠道菌群，提升消化能力。",
          "stock": 120
        },
        {
          "foodName": "时令果汁",
          "foodPrice": 15,
          "foodImage": "images/food4.png",
          "foodDescription": "时令果汁选用当季水果鲜榨而成，无添加糖分，保留水果原汁原味，营养丰富，是补充维生素的理想选择。",
          "stock": 150
        }
      ],
      selectedFoodIndex: null,
      selectedFood: null,
      purchaseMessage: '',
      detailQuantity: 1, // 详情页中的商品数量
      isCartOpen: false, // 购物车是否打开
      isMenuOpen: false, // 菜单是否打开
      lastScrollPosition: 0 // 记录滚动位置
    };
  },
  computed: {
    // 计算购物车总金额
    cartTotal() {
      return this.cart.reduce((total, item) => total + (item.food.foodPrice * item.quantity), 0);
    },
    // 计算购物车中的商品总数量
    cartItemCount() {
      return this.cart.reduce((count, item) => count + item.quantity, 0);
    },
    // 计算详情页中的小计金额
    detailSubtotal() {
      if (!this.selectedFood) return 0;
      return this.selectedFood.foodPrice * this.detailQuantity;
    }
  },
  mounted() {
    // 监听窗口大小变化，调整布局
    window.addEventListener('resize', this.adjustLayout);
    // 初始调整布局
    this.adjustLayout();

    // 初始化Bootstrap模态框
    this.initializeModals();
  },
  beforeDestroy() {
    // 移除事件监听器
    window.removeEventListener('resize', this.adjustLayout);
  },
  methods: {
    // 初始化Bootstrap模态框
    initializeModals() {
      // 使用jQuery初始化Bootstrap模态框
      // 注意：在实际Vue项目中，应该使用Vue的方式处理模态框
      // 这里保留jQuery方式是为了与原代码保持一致
    },

    // 根据窗口大小调整布局
    adjustLayout() {
      if (window.innerWidth < 768) {
        // 小视口：显示简化版卡片，垂直排列
        document.querySelectorAll('.food-card').forEach(card => {
          card.classList.add('simplified');
          // 增加阴影效果，提高在小屏幕上的可见度
          card.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
        });

        // 在小视口中可以选择隐藏某些元素以简化界面
        document.querySelectorAll('.card-text').forEach(text => {
          // 保留价格信息，但可以调整样式
          text.classList.add('small');
          text.style.fontWeight = 'bold';
        });

        // 调整按钮样式，使其在小屏幕上更突出
        document.querySelectorAll('.detail-btn').forEach(btn => {
          btn.classList.add('btn-block');
          btn.classList.remove('btn-sm');
        });
      } else {
        // 大视口：显示完整卡片，并排显示
        document.querySelectorAll('.food-card').forEach(card => {
          card.classList.remove('simplified');
          // 恢复默认阴影（仅在悬停时显示）
          card.style.boxShadow = '';
        });

        document.querySelectorAll('.card-text').forEach(text => {
          text.classList.remove('small', 'd-none');
          text.style.fontWeight = '';
        });

        // 恢复按钮样式
        document.querySelectorAll('.detail-btn').forEach(btn => {
          btn.classList.remove('btn-block');
          btn.classList.add('btn-sm');
        });
      }
    },

    // 打开购物车
    openCart() {
      $('#cartModal').modal('show');
    },

    // 显示食品详情
    showFoodDetail(index) {
      this.selectedFoodIndex = index;
      this.selectedFood = this.foods[index];
      this.purchaseMessage = '';
      this.detailQuantity = 1; // 重置数量为1

      // 使用jQuery显示模态框
      $('#foodDetailModal').modal('show');
    },

    // 从详情页增加数量
    increaseDetailQuantity() {
      if (!this.selectedFood) return;

      if (this.detailQuantity < this.selectedFood.stock) {
        this.detailQuantity += 1;
      } else {
        this.purchaseMessage = '<span class="text-danger">已达到最大库存数量！</span>';
      }
    },

    // 从详情页减少数量
    decreaseDetailQuantity() {
      if (this.detailQuantity > 1) {
        this.detailQuantity -= 1;
        // 清除可能存在的错误消息
        if (this.purchaseMessage.includes('最大库存')) {
          this.purchaseMessage = '';
        }
      }
    },

    // 从详情页添加到购物车
    addToCartFromDetail() {
      if (!this.selectedFood || this.detailQuantity <= 0) return;

      // 检查库存
      if (this.selectedFood.stock < this.detailQuantity) {
        this.purchaseMessage = '<span class="text-danger">库存不足！</span>';
        return;
      }

      // 检查购物车中是否已有该商品
      const existingItemIndex = this.cart.findIndex(item => item.food.foodName === this.selectedFood.foodName);

      if (existingItemIndex !== -1) {
        // 如果购物车中已有该商品，检查库存是否足够增加数量
        const newQuantity = this.cart[existingItemIndex].quantity + this.detailQuantity;
        if (newQuantity <= this.selectedFood.stock) {
          this.cart[existingItemIndex].quantity = newQuantity;
          this.purchaseMessage = '<span class="text-success">已添加到购物车！</span>';
        } else {
          this.purchaseMessage = '<span class="text-danger">添加数量超过库存限制！</span>';
          return;
        }
      } else {
        // 如果购物车中没有该商品，添加新项目
        this.cart.push({
          food: this.selectedFood,
          quantity: this.detailQuantity
        });
        this.purchaseMessage = '<span class="text-success">已添加到购物车！</span>';
      }
    },

    // 直接处理购买
    processPurchaseDirectly() {
      if (!this.selectedFood) return;

      // 检查库存是否足够
      if (this.selectedFood.stock >= this.detailQuantity) {
        // 创建订单
        const order = {
          name: this.selectedFood.foodName,
          quantity: this.detailQuantity,
          price: this.selectedFood.foodPrice,
          subtotal: this.selectedFood.foodPrice * this.detailQuantity,
          timestamp: new Date().toISOString()
        };

        // 保存订单到本地存储
        this.saveOrder(order);

        // 更新库存
        this.selectedFood.stock -= this.detailQuantity;

        // 显示购买成功消息
        this.purchaseMessage = '<span class="text-success">购买成功！</span>';

        // 3秒后自动关闭模态框
        setTimeout(() => {
          if (this.purchaseMessage.includes('成功')) {
            $('#foodDetailModal').modal('hide');

            // 显示结算成功模态框
            setTimeout(() => {
              $('#checkoutSuccessModal').modal('show');
            }, 500);
          }
        }, 3000);
      } else {
        // 显示库存不足消息
        this.purchaseMessage = '<span class="text-danger">库存不足，无法完成购买！</span>';
      }
    },

    // 保存订单到本地存储
    saveOrder(order) {
      let orders = [];

      // 获取现有订单
      if (localStorage.getItem('orders')) {
        orders = JSON.parse(localStorage.getItem('orders'));
      }

      // 添加新订单
      orders.push(order);

      // 保存回本地存储
      localStorage.setItem('orders', JSON.stringify(orders));

      // 在控制台显示订单信息
      console.log('新订单已创建:', order);
    },

    // 添加商品到购物车
    addToCart(index) {
      const food = this.foods[index];

      // 检查库存
      if (food.stock <= 0) {
        alert('抱歉，该商品已售罄');
        return;
      }

      // 检查购物车中是否已有该商品
      const existingItemIndex = this.cart.findIndex(item => item.food.foodName === food.foodName);

      if (existingItemIndex !== -1) {
        // 如果购物车中已有该商品，检查库存是否足够增加数量
        if (this.cart[existingItemIndex].quantity < food.stock) {
          this.cart[existingItemIndex].quantity += 1;
        } else {
          alert('抱歉，该商品库存不足');
          return;
        }
      } else {
        // 如果购物车中没有该商品，添加新项目
        this.cart.push({
          food: food,
          quantity: 1
        });
      }
    },

    // 减少购物车中商品数量
    decreaseQuantity(index) {
      if (this.cart[index].quantity > 1) {
        this.cart[index].quantity -= 1;
      } else {
        this.removeFromCart(index);
      }
    },

    // 增加购物车中商品数量
    increaseQuantity(index) {
      // 检查库存是否足够
      if (this.cart[index].quantity < this.cart[index].food.stock) {
        this.cart[index].quantity += 1;
      } else {
        alert('抱歉，该商品库存不足');
      }
    },

    // 从购物车中移除商品
    removeFromCart(index) {
      this.cart.splice(index, 1);
    },

    // 结算功能
    checkout() {
      if (this.cart.length === 0) {
        alert('购物车是空的，请先添加商品');
        return;
      }

      // 更新商品库存
      this.cart.forEach(item => {
        const foodIndex = this.foods.findIndex(food => food.foodName === item.food.foodName);
        if (foodIndex !== -1) {
          this.foods[foodIndex].stock -= item.quantity;
        }
      });

      // 清空购物车
      this.cart = [];

      // 关闭购物车模态框
      $('#cartModal').modal('hide');

      // 显示结算成功模态框
      setTimeout(() => {
        $('#checkoutSuccessModal').modal('show');
      }, 500);
    }
  }
};
</script>

<style>
.food-card {
    transition: transform 0.3s;
    height: 100%;
}
.food-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.food-img {
    height: 200px;
    object-fit: cover;
}
.bg-green {
    background-color: #4CAF50;
}
.text-green {
    color: #4CAF50;
}
.btn-outline-green {
    color: #4CAF50;
    border-color: #4CAF50;
}
.btn-outline-green:hover {
    background-color: #4CAF50;
    color: white;
}
.btn-green {
    background-color: #4CAF50;
    color: white;
}
.btn-green:hover {
    background-color: #3e8e41;
    color: white;
}
.stock-warning {
    color: #ff6b6b;
}

/* 食品卡片样式 */
.food-img-container {
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.stock-badge {
    top: 10px;
    right: 10px;
    font-size: 0.75rem;
    z-index: 10;
}

.cart-badge {
    top: -5px;
    right: -5px;
    font-size: 0.7rem;
    padding: 0.25rem 0.4rem;
}

/* 食品详情样式 */
.food-detail-img-container {
    position: relative;
    max-height: 250px;
    overflow: hidden;
}

.price-tag {
    bottom: 15px;
    left: 0;
    font-weight: bold;
    z-index: 10;
}

.food-description {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #555;
}

/* 按钮动画效果 */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 添加到购物车按钮 */
.add-cart-btn {
    font-size: 0.8rem;
}

/* 响应式布局样式 */
@media (max-width: 767.98px) {
    /* 小视口样式 - 垂直排列 */
    .food-card {
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .food-img {
        height: 200px;
        width: 100%;
    }
    .card-title {
        font-size: 1.2rem;
        margin-top: 0.5rem;
    }
    .simplified .card-body {
        padding: 1rem;
    }
    .detail-btn {
        width: 80%;
        margin: 0.5rem auto;
    }
    /* 确保容器有足够的内边距 */
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    /* 增加底部间距以便于滚动浏览 */
    #food-section {
        padding-bottom: 2rem;
    }
}

@media (min-width: 768px) and (max-width: 991.98px) {
    /* 中等视口样式 - 两列排列 */
    .food-img {
        height: 180px;
    }
    .food-card {
        margin-bottom: 1rem;
    }
    /* 两列布局的额外样式 */
    .col-md-6 {
        padding-left: 10px;
        padding-right: 10px;
    }
}

@media (min-width: 992px) {
    /* 大视口样式 - 四列并排 */
    .food-card {
        margin-bottom: 0;
        height: 100%;
    }
    .food-img {
        height: 180px;
    }
    /* 确保卡片有足够的间距 */
    .col-lg-3 {
        padding-left: 10px;
        padding-right: 10px;
    }
}
</style>
